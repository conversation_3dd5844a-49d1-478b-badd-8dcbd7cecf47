@import url("https://fonts.googleapis.com/css?family=Open+Sans|Poppins:400,500,700");
@import url("../../stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css");
body {
  font-family: "Open Sans", sans-serif;
  font-weight: 400;
  color: #757575;
  font-size: 15px;
  line-height: 28px;
  letter-spacing: 0.4px;
  overflow-x: hidden;
  /* zoom: 0.8; */
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #444;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  letter-spacing: 0;
  line-height: normal;
  margin-bottom: 20px;
  margin-top: 0;
}
p {
  margin-bottom: 20px;
}
ul p {
  margin-bottom: 5px;
}
section {
  padding: 60px 0;
}
/*--------------------------------------------*/
header {
  position: absolute;
  width: 100%;
  z-index: 9999;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}

.banner {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
}

#myVideo {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

.navtags {
  text-align: center;
  /* padding-left: 10vh; */
}

.navbar-sidebutton-con {
  display: flex;
  align-items: center;
  padding-right: 4px;
  gap: 4px;
  /* margin: 3vh;  */
}
.navbar-sidebutton-con li {
  min-width: 132px;
}
.content {
  position: relative;
  bottom: 0;
  z-index: 999;
  background: white;
  color: #f1f1f1;
  width: 100%;
}
.navbar-inverse {
  border-radius: 0;
  margin-bottom: 0;
  width: 100%;
  padding-left: 7vw;
  background: linear-gradient(
    180deg,
    rgba(20, 30, 30, 0.95),
    rgba(60, 70, 70, 0.85)
  );
  border: none;
}
.navbar-inverse.isStuck {
  background: #fff;
  position: fixed;
  box-shadow: 0px 2px 10px #aaa;
}
.header-top {
  background-color: #222;
}

.navbar-logo {
  min-height: 100%;
  transform: scale(1.3);
  margin-top: -10px;
}

.navbar-nav {
  padding: 12px 0;
  display: flex;
  align-items: center;
}
.navbar-inverse .navbar-nav > li > a {
  font-weight: 100;
  color: #ffff;
  font-family: "Poppins", sans-serif;
}
.navbar-inverse .navbar-nav > li > a:focus,
.navbar-inverse .navbar-nav > li > a:hover {
  text-decoration: underline;
}
.navbar-inverse.isStuck .navbar-nav > li > a:focus,
.navbar-inverse.isStuck .navbar-nav > li > a {
  color: #306fbc;
}
.navbar-inverse.isStuck .navbar-nav > li > a:hover {
  text-decoration: underline;
}

.section-header-text {
  background: #306fbc;
}
.header_left {
  float: left;
  margin: 0.2em 0 0;
}
.header_left ul {
  margin-bottom: 5px;
  padding-left: 0;
}
.header_left ul li {
  display: inline-block;
  margin-right: 0.8em;
  font-size: 14px;
  color: #f9f9f9;
}
.header_left ul li span {
  padding-right: 1em;
  color: #f9f9f9;
}
.header_left ul li a {
  color: #f9f9f9;
  text-decoration: none;
}
.header_left ul li a:hover {
  color: #fff;
}
.header_right {
  float: right;
}
.social-icons.text-left a {
  display: inline-block;
}
.social-icons a {
  background: transparent;
  padding: 3px 10px;
  color: #fff;
  margin-right: 0.5px;
  transition: 0.5s all;
  -webkit-transition: 0.5s all;
  -o-transition: 0.5s all;
  -ms-transition: 0.5s all;
  -moz-transition: 0.5s all;
}
.social-icons a:hover {
  background: #fff;
  color: #aa0202;
  border: 1px solid #efefef;
  transition: 0.5s all;
  -webkit-transition: 0.5s all;
  -o-transition: 0.5s all;
  -ms-transition: 0.5s all;
  -moz-transition: 0.5s all;
}
.navbar-right .dropdown-menu {
  right: unset;
}

/* index triangle image style  */

#triangle-image-index {
  transform: scale(1.3);
}

.social-media-icons {
  display: flex;
  gap: 20px;
}

.media-icon {
  width: 30px;
  height: 30px;
  filter: invert(100%);
}

/*--------------------------------------------*/
.inner-banner {
  background-image: url(../images/inner.png);
  background-size: cover;
  background-position: inherit;
  padding: 180px 0 120px;
}
.inner-banner.audit {
  background-image: url(../images/inner-1.png);
}
.inner-banner.training {
  background-image: url(../images/inner-2.jpg);
}
.inner-banner.electronic {
  background-image: url(../images/inner-3.png);
}
.inner-banner.digital {
  background-image: url(../images/inner-4.jpg);
}

.inner-banner h1 {
  font-size: 70px;
  color: #111;
  text-shadow: 2px 2px 2px #eee;
  margin-bottom: 0;
}
.inner-banner h4,
.inner-banner h4 a {
  color: #222;
  letter-spacing: 4px;
}

/*--------------------------------------------*/
canvas {
  background-image: url(../images/1.jpg);
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  width: 100%;
}

.slider-content h2 {
  color: #fff;
  text-shadow: 2px 2px 3px #333;
  font-size: 45px;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 10px;
  display: inline-block;
  margin-top: 10px;
  line-height: 60px;
}
.slider-content p {
  color: #fff;
  font-size: 22px;
  text-shadow: 2px 2px 3px #333;
  font-weight: 400;
  margin-bottom: 35px;
}

.version-section {
  margin-top: 88px;
}
.welcome-section h2 {
  color: #111;
  font-size: 36px;
  font-weight: 300;
  line-height: 1.4em;
  margin-bottom: 70px;
  position: relative;
  text-align: center;
}
.welcome-section h3 {
  font-size: 20px;
  margin-bottom: 15px;
  padding: 0 15px;
  text-transform: capitalize;
  text-align: left;
  color: #306fbc;
}
.welcome-section p {
  line-height: 26px;
  padding: 0 15px;
  text-align: left;
}
.cardContainer {
  display: flex;
  padding: 0vw 1.5vw;
}
.cardContainer-version {
  display: flex !important;
  padding: 0vw 8vw !important;
}

/* Horizontal scroll styles only for service-section */
.service-section .cardContainer-version {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
  width: 92vw;
}

.service-section .cardContainer-version .service-card {
  flex: 0 0 calc(28% - 20px);
  margin-right: 15px;
  padding-top: 15px;
  white-space: normal;
}

.service-section .cardContainer-version .service-card:last-child {
  margin-right: 0;
}

/* Custom scrollbar styling only for service-section */
.service-section .cardContainer-version::-webkit-scrollbar {
  height: 8px;
}

.service-section .cardContainer-version::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.service-section .cardContainer-version::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.service-section .cardContainer-version::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.row {
  display: flex;
  padding: 0vw 8vw;
  margin: 0 !important;
}
.row-alignment {
  margin-right: -15px;
  margin-left: -15px;
}
.home-page-card {
  box-shadow: 0 0 20px 3px rgba(0, 0, 0, 0.2);
  /* padding: 20px 20px ; */
  position: relative;
  transition: all 0.3s ease 0s;
  z-index: 1;
  height: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.home-page-card h3 {
  padding-top: 20px;
}
.home-page-card-flex {
  justify-content: flex-start;
}
.home-page-card-flex img {
  padding-top: 20px;
}
.home-page-card-flex p {
  padding: 0px 20px;
}
ul .li-custome::marker {
  content: attr(value);
}
.li-custome {
  text-align: left;
  padding-left: 2px;
  font-weight: 500;
  font-size: 18px;
  color: #444444;
}

.navbar-container {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.nav-link {
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;

  .navbar-button2 {
    background-color: #306fbc;
    padding: 10px 15px;
    border-radius: 12px;
    color: white;
    text-decoration: none;
  }
}

.custome-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 5px;
  display: flex;
  flex-wrap: wrap;
}
.home-page-card img {
  margin-bottom: 22px;
  display: flex;
  flex-direction: column;
}
.home-page-card:hover,
.home-page-card.active {
  background: #5d92d3;
}
.home-page-card:hover h3,
.home-page-card:hover .service-card-top h4,
.home-page-card:hover p,
.home-page-card.active h3,
.home-page-card.active p,
.home-page-card:hover li {
  color: #fff;
}
.gray {
  background-color: #ffff;
}
/*----------------------------------------------------*/
.section_title {
  width: 100%;
  margin-bottom: 50px;
  margin-top: 50px;
  line-height: 1.4;
}
.section-para {
  font-size: 18px;
  margin-bottom: 0px;
  text-align: center !important;
  padding-bottom: 13px !important;
}
.cards-top h3 {
  text-align: center !important;
}
.font-16 {
  font-size: 16px;
}

.section_title h2 {
  font-size: 38px;
  font-weight: 500;
  margin-bottom: 0;
  margin-top: 20px;
  color: #222;
  text-transform: capitalize;
}
.section_title p {
  margin-top: 10px;
}
/*---------------------------------------------------*/
.service-box {
  box-shadow: 0 0 74px rgba(10, 10, 10, 0.07);
  -moz-box-shadow: 0 0 74px rgba(10, 10, 10, 0.07);
  -webkit-box-shadow: 0 0 74px rgba(10, 10, 10, 0.07);
  margin-bottom: 50px;
}
.service-box h4 {
  font-size: 17px;
  margin-bottom: 8px;
  text-transform: uppercase;
}
.service-box .icon {
  margin-left: 20px;
  margin-top: -30px;
  background-color: #fff;
  padding: 10px 15px;
}
.service-text {
  padding: 30px 22px 22px 22px;
}
.service-box a {
  color: #306fbc;
}
.service-box a:hover {
  text-decoration: none;
}
.expert-list-container {
  width: 100%;
}
.expert-list {
  font-weight: bold;
}
.expert-list h4 {
  color: #444;
  font-size: 18px;
  font-weight: 100;
  font-family: "Open Sans" sans-serif;
}
/*---------------------------------------------------*/
/*----- testimonial css  -----*/
#testimonial_area.section_padding.bg_white {
  padding-bottom: 111px;
  padding-top: 0;
}
.testimonial {
  padding: 0 20px;
  margin: 50px 10px;
}
.testimonial .pic {
  width: 122px;
  height: 122px;
  float: left;
  margin-top: 20px;
  margin-right: 50px;
  position: relative;
}
.team-div .testimonial .pic {
  width: 180px;
  height: 180px;
  float: left;
  margin-top: 20px;
  margin-right: 50px;
  position: relative;
}
.testimonial .pic::before,
.testimonial .pic::after {
  content: "";
  display: block;
  height: 50%;
  position: absolute;
  width: 100%;
}
.testimonial .pic:before {
  bottom: -10%;
  left: -10%;
  border-bottom: 3px solid #110641;
  border-left: 3px solid #110641;
}
.testimonial .pic:after {
  top: -10%;
  right: -10%;
  border-top: 3px solid #110641;
  border-right: 3px solid #110641;
}
.testimonial .pic img {
  width: 100%;

  height: auto;
}
.testimonial .testimonial-content {
  display: table;
  position: relative;
}
.testimonial .testimonial-content::before {
  color: transparent;
  content: "\f10d";
  font-family: fontawesome;
  font-size: 112px;
  margin-right: 20px;
  position: absolute;
  right: 50%;
  top: 54px;
  transform: rotate(180deg);
  z-index: -1;
}
.testimonial .testimonial-title {
  font-size: 18px;
  color: #222;
  text-transform: capitalize;
  font-size: 24px;
}
.testimonial .post {
  font-size: 20px;
  font-weight: 600;
  color: #555;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  margin-left: 5px;
  padding-left: 5px;
}
.testimonial .description {
  color: #454545;
  margin-top: 12px;
  margin-bottom: 10px;
  font-style: italic;
  /* min-height: 135px; */
  font-size: 18px;
}
.owl-theme .owl-controls .owl-page.active span,
.owl-theme .owl-controls.clickable .owl-page:hover span {
  background-color: #110641;
}
@media only screen and (max-width: 1096px) {
  .inner-banner h1 {
    font-size: 60px;
  }
  .navbar-brand {
    height: 50px;
    padding: 0 10px;
  }
  .navbar-logo {
    height: 100%;
    transform: scale(1.3);
  }
  #triangle-image-index {
    transform: scale(1);
  }

  .managing-section-title {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .managing-section-title h2 {
    width: 80%;
    font-weight: 600;
    font-size: 28px;
  }
}
.homepagefooter {
  padding: 0 8vw;
  margin-left: 0 !important;
  width: 100%;
}

@media only screen and (max-width: 980px) {
  .navbar-container {
    width: 100%;
    display: inline-block;
  }
  .navbar-brand {
    height: 50px;
    padding: 0 10px;
  }
  .navbar-logo {
    min-height: 100%;
    transform: scale(1.3);
  }
  #triangle-image-index {
    transform: scale(1);
  }
  .cardContainer {
    display: block;
  }
  .custome-container {
    width: 100%;
    padding: 0 0.5%;
  }
  .testimonial {
    padding: 20px 0;
    margin: 20px 15px;
  }
  .testimonial .pic {
    float: none;
  }
  .testimonial .testimonial-content {
    display: block;
    margin-top: 30px;
  }
  .testimonial .testimonial-content:before {
    z-index: 1;
    top: -200px;
  }
  .managing-section-title {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .managing-section-title h2 {
    width: 80%;
    font-weight: 600;
    font-size: 28px;
  }
  .version-section {
    margin-top: -60px;
  }
}
#testimonial-slider .carousel-indicators li {
  border: 1px solid #110641;
  border-radius: 3px;
  display: inline-block;
  height: 6px;
  margin: -25px 5px;
  width: 19px;
}
#testimonial-slider .carousel-indicators .active {
  background: #110641;
}
/*---------------------------------------------------------------*/
.portfolio {
  padding: 40px 0 0;
}
.portfolio img {
  width: 100%;
  /* filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%); */
  /* transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s; */
}
.portfolio .no-p {
  overflow: hidden;
}
.portfolio img:hover {
  /* transform: scale(1.15);
  -webkit-transform: scale(1.15);
  -moz-transform: scale(1.15);
  -o-transform: scale(1.15); */
  /* filter: grayscale(0%);
  -webkit-filter: grayscale(0%);
  -moz-filter: grayscale(0%);
  -o-filter: grayscale(0%); */
  overflow: hidden;
}
/*----------------------------------------------------------------*/
.post tr {
  border-bottom: 1px solid #ccc;
}
.post tr:last-child {
  border: none;
}
.post th {
  vertical-align: top;
  width: 20%;
  padding: 5px;
  color: #333;
}
.purple-bg {
  background-color: #306fbc;
  padding: 30px 15px 10px;
  color: #fff;
}
.purple-bg h3 {
  color: #fff;
  font-size: 28px;
  font-weight: 100;
}
.orange-bg {
  background-color: #306fbc;
  padding: 30px 15px 10px;
  color: #fff;
}
.orange-bg h3 {
  color: #fff;
  font-size: 28px;
  font-weight: 100;
}
.orange-bg p {
  font-size: 17px;
}
.orange-bg a {
  background-color: #000;
  color: #fff;
  padding: 5px 8px;
}
.service-img {
  padding: 10px 15px 20px;
}
/*----------------------------------------------------------------*/
.contact_info_parent {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.contact_info {
  margin-bottom: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  gap: 10px;
  align-items: center;
}
.contact_info img {
  height: 20px;
  position: relative;
  top: 1px;
}
.contact_info p {
  margin: 0;
}
/*----------------------------------------------------------------*/
footer {
  padding: 50px 0 0;
  background-color: #222;
  background-image: url(../images/footer-bg.png);
  background-position: center;
  background-size: cover;
  min-height: 400px;
  color: #ededed;
}
footer a {
  color: #ff5d00;
  font-weight: bold;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s;
}
footer a:hover {
  color: #fff;
}
footer ul {
  padding-left: 0;
  list-style: none;
}
footer h4 {
  color: white;
  font-size: 28px;
  letter-spacing: 0.6px;
}
footer ul li {
  padding: 3px 0;
}
footer ul a {
  color: #eee;
  font-weight: normal;
  transition: 0.3s;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -o-transition: 0.3s;
}
footer ul a:hover {
  color: rgba(48, 111, 188, 1);
  margin-left: 10px;
  text-decoration: none;
}
footer strong {
  color: rgba(48, 111, 188, 1);
  text-decoration: underline;
}
.footer-social {
  font-size: 30px;
  vertical-align: middle;
}
.ml-0:hover {
  margin-left: 0;
}
.no-p {
  padding: 0;
}
footer .copyright {
  margin-top: 20px;
}
.social-icon {
  margin: 0 7px;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #110641;
}
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border: none;
}

@media only screen and (max-width: 965px) {
  .navbar-brand {
    height: 40px;
    padding: 0 15px;
  }
  #triangle-image-index {
    transform: scale(1);
  }
  .navbar-logo {
    min-height: 100%;
    transform: scale(1.3);
  }
  .navbar-inverse .navbar-nav > li > a {
    font-size: 18px;
    padding: 15px 8px;
  }
}

@media only screen and (max-width: 875px) {
  .navbar-brand {
    height: 40px;
    padding: 0 15px;
  }
  #triangle-image-index {
    transform: scale(1);
  }
  .navbar-logo {
    min-height: 100%;
    transform: scale(1.3);
    margin-top: 18px;
  }
  .navbar-inverse .navbar-nav > li > a {
    font-size: 16px;
    padding: 15px 8px;
  }
}

@media (max-width: 768px) {
  .home-page-card {
    min-height: unset;
  }
  .navbar-brand {
    height: 40px;
    padding: 0 15px;
  }
  #triangle-image-index {
    transform: scale(1);
  }
  .navbar-logo {
    min-height: 100%;
    transform: scale(1.3);
    margin-top: 5px;
  }
  .navbar-header {
    width: 100%;
  }
  .navbar-inverse .navbar-nav > li > a {
    font-size: 14px;
    padding: 15px 8px;
  }
  .testimonial .pic {
    margin: 0 auto;
  }
  .testimonial .testimonial-title {
    text-align: center;
  }
  .team-div .testimonial .pic {
    width: 122px;
    height: 122px;
    margin: 0 auto;
    float: none;
  }
  .service-img {
    max-width: 400px;
  }
  .managing-section-title {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .managing-section-title h2 {
    width: 80%;
    font-weight: 600;
    font-size: 28px;
  }
}
@media (max-width: 480px) {
  section {
    padding: 30px 0;
  }
  .navbar-brand > img {
    max-height: 55px;
  }
  #triangle-image-index {
    transform: scale(1);
  }
  .header_right {
    position: absolute;
    right: 15px;
    margin-top: 40px;
  }
  .slider-content h2 {
    font-size: 36px;
    line-height: 50px;
  }
  #myCarousel .item p {
    font-size: 16px;
  }
  .navbar-inverse {
    background: linear-gradient(
      180deg,
      rgba(20, 30, 30, 0.95),
      rgba(60, 70, 70, 0.85)
    );
  }
  .testimonial .pic {
    margin: 0 auto;
  }
  .testimonial .description {
    text-align: justify;
  }
  .team-div .testimonial .pic {
    width: 122px;
    height: 122px;
    margin: 0 auto;
    float: none;
  }
  .inner-banner {
    padding: 200px 0 80px;
  }
  .inner-banner h1 {
    font-size: 40px;
  }
  .home-page-card {
    padding: 40px 15px 40px;
  }
  .inner-banner h4,
  .inner-banner h4 a {
    letter-spacing: 0;
  }
  .contact_info p {
    padding-left: 50px;
    float: none;
  }
  .service-img {
    max-width: 100%;
    padding: 10px 0;
    float: none !important;
  }
  .section_title {
    margin-bottom: 20px;
  }
  .slider-content {
    margin: 25vh 0 0;
  }
  .managing-section-title {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .managing-section-title h2 {
    width: 80%;
    font-weight: 600;
    font-size: 28px;
  }
}
@media (max-width: 320px) {
  .slider-content {
    margin: 15vh 0 0;
  }
  .managing-section-title {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .managing-section-title h2 {
    width: 80%;
    font-weight: 600;
    font-size: 28px;
  }
}

@media (max-width: 299px) {
  .navbar-brand > img,
  .navbar-logo {
    /* max-height: 80px; */
    min-height: 40px;
  }
  .navbar-logo {
    height: 90%;
  }
}

@media (min-width: 768px) {
  .navbar-nav > li > a {
    padding-top: 20px;
    padding-bottom: 15px;
  }
}

@media only screen and (max-width: 1450px) {
  .nav > li > a {
    padding: 10px 4px !important;
    font-size: 12px !important;
  }
  .navbar-logo {
    padding-right: 8px;
  }
}
@media only screen and (max-width: 1300px) {
  .navbar-inverse {
    padding-left: 2vw;
  }
  .homepagefooter {
    padding: 0 3vw;
  }
  .row {
    padding: 0vw 2vw;
  }
}
@media only screen and (max-width: 1200px) {
  .navbar-inverse {
    padding-left: 0vw;
  }

  .navbar-collapse {
    padding-left: 0vw;
  }
  .row {
    padding: 0vw 0vw;
  }
  .desktop-header {
    display: none;
  }
  .mobile-header {
    display: block !important;
  }
  .banner {
    height: auto;
  }

  #myVideo {
    width: 100%;
    height: auto;
  }
  .cardContainer-version {
    flex-wrap: wrap;
  }
  /* change display to block for responsive */
  .service-section .cardContainer-version {
    display: block;
  }
  .cardContainer-version .col-md-4,
  .cardContainer-version .col-sm-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Mobile responsive styles for service-section horizontal scroll */
  /* .service-section .cardContainer-version {
    flex-wrap: nowrap;
    padding: 0vw 2vw !important;
  }

  .service-section .cardContainer-version .service-card {
    flex: 0 0 calc(80vw - 15px);
    min-width: 250px;
    margin-right: 15px;
  } */

  .cards-top {
    min-height: 62px !important;
  }
  .service-card-top {
    min-height: 100px !important;
  }

  .autoprice-buttons {
    min-height: 54px !important;
    min-width: 270px !important;
  }
  .autoprice-content1 {
    flex-direction: column;
    align-items: center;
  }
  .autoprice-content1 img {
    width: 100%;
    padding-bottom: 10px;
  }
  .homepagefooter {
    display: flex;
    flex-direction: column;
    padding: 0vw 5vw;
  }
  .homepagefooter > .col-md-5,
  .homepagefooter > .col-md-3,
  .homepagefooter > .col-md-4 {
    width: 100%;
    margin-bottom: 20px;
  }
  .col-sm-6.no-p {
    width: 100%;
  }
  .social-media-icons {
    text-align: center;
  }

  .autoprice-content p {
    text-align: justify;
  }
  .res-img {
    width: 250px;
  }
  .res-img1 {
    width: 140px;
    height: 208px;
  }
  .expert-parent {
    flex-direction: column;
    align-items: center;
    padding: 5vw 5vw 0vw 5vw;
    gap: 10px;
  }
  .expert-content {
    width: 90%;
    align-items: center;
    padding: 5px 24px;
  }
  .expert-div {
    width: 100%;
  }
  .ex-div1 {
    padding-top: 24px;
  }
  #ham-image {
    height: 40px !important;
  }
}
@media only screen and (max-width: 420px) {
  .autoprice-header {
    gap: 0px !important;
    flex-direction: column-reverse;
  }
  .autoprice-header h1 {
    font-size: 30px !important;
  }
  .res-img {
    width: 100% !important;
  }
  .mobile-list {
    padding: 5vw 5vw 9vw !important;
  }
  .reverse-image {
    flex-direction: column-reverse;
  }
}

.center-list-items {
  text-align: center;
  display: inline-block;
  margin: 0 auto;
  padding-bottom: 15px;
}
.autoprice-header {
  display: flex;
  padding: 2vw 9vw 0vw 9vw;
  gap: 30px;
  justify-content: space-between;
  font-family: "Poppins";
  font-size: 16px;
}
.blue-heading {
  color: #004aad;
}
.autoprice-header h1 {
  font-size: 40px;
}
.autoprice-header-leftside {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.autoprice-buttons-parent {
  display: flex;
  gap: 10px;
  padding: 0px 5px 18px;
  flex-wrap: wrap;
}
.autoprice-buttons {
  background-color: #306fbc;
  display: flex;
  padding: 8px 11px;
  color: white;
  border-radius: 8px;
  align-items: center;
  gap: 10px;
  min-height: 50px;
  min-width: 260px;
  max-width: 270px;
}
.autoprice-buttons p {
  margin-bottom: 0 !important;
  line-height: 20px !important;
  font-size: 14px !important;
}
.autoprice-buttons img {
  height: 20px;
}
.autoprice-content {
  padding: 0vw 9vw 6vw;
  font-family: "Poppins";
  font-size: 16px;
}
.autoprice-content1 {
  display: flex;
  gap: 10px;
  padding-bottom: 25px;
}
.autoprice-subheadings {
  color: #757575;
  /* font-weight: bold; */
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 0px !important;
}

.remove-p-margin {
  margin-bottom: 0 !important;
}
.card_screens_img {
  width: 40vw;
}
.service-card {
  padding-right: 0 !important;
  margin-bottom: 10px;
}
.service-card h4 {
  padding: 0px 20px;
}
.li-custome {
  font-size: 14px !important;
  padding: 0px 15px;
}
.service-card-top {
  min-height: 254px;
}
.service-card-top h4 {
  font-size: 14px;
}
.service-card-top:hover h4 {
  color: white;
}
.cards-top {
  min-height: 130px;
}
.card-images {
  height: 150px;
  width: 250px;
}
.service-image {
  height: 65px;
  width: 65px;
}
.expert-parent {
  display: flex;
  padding: 5vw 13vw 0vw 13vw;
  width: 100%;
  justify-content: space-between;
}
.expert-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 420px;
}
.expert-content h4 {
  margin-bottom: 5px !important;
}
.expert-div {
  display: flex;
  gap: 10px;
}

.dropdown:hover .dropdown-menu {
  display: block;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  transform: translateX(-30%);
  min-width: 250px;
  padding: 10px 0;
}
.dropdown-menu li a {
  line-height: 1.6;
}
.dropdown > .dropdown-toggle {
  pointer-events: none;
}
.services-p p {
  margin-bottom: 0 !important;
}
.service-headings {
  font-weight: bold;
}
.remove-margin p {
  margin-bottom: 0 !important;
}

.mobile-header {
  display: none;
}
.mobile-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.3vw 6vw;
  background: linear-gradient(
    180deg,
    rgba(20, 30, 30, 0.95),
    rgba(60, 70, 70, 0.85)
  );
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

.mobile-img {
  height: 45px;
}

#mobileMenu {
  width: 100%;
  height: 0;
  background: linear-gradient(
    180deg,
    rgba(20, 30, 30, 0.95),
    rgba(60, 70, 70, 0.85)
  );
  color: white;
  overflow: hidden;
  transition: height 0.4s ease;
  z-index: 999;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

#mobileMenu.open {
  height: fit-content;
}

.hamburg-list {
  position: absolute;
  top: 10px;
  right: 10px;
}
.mobile-list {
  list-style: none;
  padding: 2vw 7vw;
  margin: 0;
}

.mobile-list-items {
  padding: 7px 0;
  font-size: 18px;
}
.mobile-list-items a {
  color: white;
  text-decoration: none;
}
.dropdown-m {
  position: relative;
}

.dropdown-menu-m {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 5px;
  background: linear-gradient(
    180deg,
    rgba(20, 30, 30, 0.95),
    rgba(60, 70, 70, 0.85)
  );
  color: black;
  list-style: none;
  padding: 10px 20px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  min-width: 180px;
  z-index: 9999;
}
.mobile-list-items > a:focus,
.mobile-list-items > a:active {
  background-color: transparent !important;
  color: inherit !important;
  outline: none !important;
  box-shadow: none !important;
}

.dropdown-m.active .dropdown-menu-m {
  display: block;
}

.blue-bold {
  color: #004aad;
  font-weight: bold;
}
.heading-about-us {
  font-size: 64px;
  margin-top: 50px !important;
}
.section-header {
  padding: 0 9vw;
}
