<!DOCTYPE html>
<html lang="en">
<!-- Added by HTTrack -->
<meta http-equiv="content-type" content="text/html;charset=ISO-8859-1" /><!-- /Added by HTTrack -->

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>Careers: Be a Part of our Precium Team in Pune, Maharashtra </title>
	<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,user-scalable=no">
	<meta name="description"
		content="Career at Precium: If you want to make your career in Precium (Revenue Management for Hotel Industry), then explore your talent here. Please drop your details in the below form. We get back to your shortly.  " />
	<meta name="keywords" content=" " />
	<link rel="canonical" href="https://precium.in/careers" />
	<link rel="apple-touch-icon" href="images/logo-purple.svg" />
    <link rel="icon" href="images/logo-purple.ico" />
	<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
		integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.3/dist/umd/popper.min.js"
		integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.1.3/dist/js/bootstrap.min.js"
		integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
		crossorigin="anonymous"></script>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css"
		integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

	<link
	rel="stylesheet"
	href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css"
	/>

	<link rel="stylesheet" type="text/css" href="/css/style.css">
		<link rel="stylesheet" type="text/css" href="/careers/style.css">

	<script src="/js/stickUp.min.js"></script>
	<script src="/js/header.js"></script>
	<script src="/js/footer.js"></script>

</head>

<body>
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KPC9WJ3" height="0" width="0"
			style="display:none;visibility:hidden"></iframe></noscript>
	<my-header></my-header>
	<div class="cheader">
		<h1 style="padding-top: 95px; color: #004AAD; font-size: 64px;"> Join our team!</h1>
		<img src="/images/Careers Page.svg" alt="">
	</div>
	<div class="c-content">
		<p>

			Joining the Precium team means becoming a part of a dynamic, collaborative, and growth-
			focused environment. You will get the opportunity to work on varied projects within the
			exciting hospitality sector, receive valuable mentorship, gain exposure to cutting-edge
			revenue management technologies, and be duly recognised for your contributions. Take the
			step forward and join a team that truly values your professional growth and offers a solid
			platform for you to thrive in the fast-paced world of hotel revenue management.
		</p>
		
		<div class="form-container">
		<div id="form-card">
			<form onsubmit="getFormValue(event)">
				<p class="form-heading">Submit your CV/Resume</p>
			<div class="row-fields">
				<div class="max-w">
					<label for="fname">Full Name <span class="c-red">*</span></label>
					<input id="fname" name="fname" type="text" placeholder="Enter Full Name" required>
				</div>
				<div class="max-w">
					<label for="email">Email Address <span class="c-red">*</span></label>
					<input id="email" name="email" type="email" placeholder="Enter Email Address" 
					required pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" 
					title="Please enter a valid email address">
				</div>
			</div>
			<div class="row-fields">
				<div class="max-w phone-wrapper">
					<label for="phoneno">Phone Number <span class="c-red">*</span></label>
					<input id="phoneno" name="phoneno" type="tel" placeholder="Enter Phone Number" required pattern="^\+?[1-9]\d{1,14}$" title="Please enter a valid phone number">
				</div>
				<div class="max-w">
					<label for="linkedin">LinkedIn URL</label>
					<input id="linkedin" name="linkedin" type="url" placeholder="Enter LinkedIn URL">
				</div>
			</div>

			<label>Resume <span class="c-red">*</span></label>
			<div class="upload-box">
				<label for="resume" id="resume-label" class="upload-label" >
					<img class="upload-img" src="/images/Group 100.svg" alt="Upload Resume">
					<div class="file-upload-text">Upload file here <br>(only .pdf,.doc,.docx)</div>
				</label>
				<input type="file" id="resume" name="resume" style="display: none;">
				<div class="filename-wrapper">
					<span id="resume-filename" class="upload-filename"></span>
					<button type="button" id="resume-delete-btn" class="del-uploads">
						<img src="/images/del_upload.svg" alt="Delete" class="del-uploads-img">
					</button>
				</div>



			</div>
		

			<label >Cover Letter</label>
			<textarea id="cover-letter-text" placeholder="Enter Cover Letter"></textarea>

			<div class="or-divider">OR</div>

			<div class="upload-box">
				<label for="cover-letter-file" id="cover-letter-label" class="upload-label">
					<img class="upload-img" src="/images/Group 100.svg" alt="Upload Cover Letter">
					<div class="file-upload-text">Upload file here <br>(only .pdf,.doc,.docx)</div>
				</label>
				<input type="file" id="cover-letter-file" style="display: none;">
				<div class="filename-wrapper">
					<span id="cover-letter-filename" class="upload-filename"></span>
					<button type="button" id="cover-letter-delete-btn" class="del-uploads" style="display: none;">
						<img class="del-uploads-img" src="/images/del_upload.svg" alt="Delete" >
					</button>
				</div>
			</div>
		

			<label>How did you hear about us? <span class="c-red">*</span></label>
			<select id="hear-about" style="max-width: 100%;">
				<option value="">--Select--</option>
				<option value="precium-website">Premium Website</option>
				<option value="linkedin">LinkedIn</option>
				<option value="referral">Referral</option>
				<option value="other">Other</option>
			</select>
			<input type="text" id="other-source" placeholder="Please specify other"/>
			
			<p class="check-desc">
				By submitting your CV, you consent to Precium Technologies storing your data for potential
				future recruitment opportunities. We will only contact you if there's a suitable role that
				matches your qualifications.
			</p>
			<div class="checkbox" >
				<input type="checkbox" id="terms" required >
				<label for="terms" class="terms-desc" style="margin: 0;">I Agree to Terms & Conditions</label>
			</div>

			<div style="text-align: center;">
				<button type="submit" id="submit-btn" >Submit</button>
			</div>
			
  		</form>
		</div>
		<div id="thanku-card" style="display: none;">
			<p>
				Thank you for your interest in Precium Technologies. We look forward to reviewing your
				information. While we may not have openings at the moment, we keep all CVs/Resumes for
				future reference, and our recruitment team will be in touch if a role matches your
				qualifications.
			</p>
			<div style="text-align: center;">
				<button id="ok-btn">Okay</button>
			</div>

		</div>
		</div>
		<script>
				document.getElementById('resume').addEventListener('change', function () {
					var fileInput = this;
					var fileNameSpan = document.getElementById('resume-filename');
					var label = document.getElementById('resume-label');
					var deleteBtn = document.getElementById('resume-delete-btn');

					if (fileInput.files && fileInput.files.length > 0) {
						var file = fileInput.files[0];
						var fileName = file.name;
						var fileExtension = fileName.split('.').pop().toLowerCase();
						var allowedExtensions = ['pdf', 'doc', 'docx'];

						if (!allowedExtensions.includes(fileExtension)) {
						alert('Invalid file type, only PDF, DOC, or DOCX files are allowed.');
						fileInput.value = '';
						fileNameSpan.textContent = '';
						fileNameSpan.style.display = 'none';
						label.style.display = 'inline-block';
						deleteBtn.style.display = 'none';
						return;
						}

						fileNameSpan.textContent = fileName;
						fileNameSpan.style.display = 'inline-block';
						label.style.display = 'none';
						deleteBtn.style.display = 'inline-block';
					} else {
						fileNameSpan.textContent = '';
						fileNameSpan.style.display = 'none';
						label.style.display = 'inline-block';
						deleteBtn.style.display = 'none';
					}
					});
			
				document.getElementById('resume-delete-btn').addEventListener('click', function() {
					var fileInput = document.getElementById('resume');
					var fileNameSpan = document.getElementById('resume-filename');
					var label = document.getElementById('resume-label');
					this.style.display = 'none';
					fileInput.value = '';
					fileNameSpan.textContent = '';
					fileNameSpan.style.display = 'none';
					label.style.display = 'inline-block';
				});

				document.getElementById('cover-letter-file').addEventListener('change', function () {
				var fileInput = this;
				var fileNameSpan = document.getElementById('cover-letter-filename');
				var label = document.getElementById('cover-letter-label');
				var deleteBtn = document.getElementById('cover-letter-delete-btn');

				if (fileInput.files && fileInput.files.length > 0) {
					var file = fileInput.files[0];
					var fileName = file.name;
					var fileExtension = fileName.split('.').pop().toLowerCase();
					var allowedExtensions = ['pdf', 'doc', 'docx'];

					if (!allowedExtensions.includes(fileExtension)) {
						alert('Invalid file type, only PDF, DOC, or DOCX files are allowed.');
						fileInput.value = '';
						fileNameSpan.textContent = '';
						fileNameSpan.style.display = 'none';
						label.style.display = 'inline-block';
						deleteBtn.style.display = 'none';
						return;
					}

					fileNameSpan.textContent = fileName;
					fileNameSpan.style.display = 'inline-block';
					label.style.display = 'none';
					deleteBtn.style.display = 'inline-block';
				} else {
					fileNameSpan.textContent = '';
					fileNameSpan.style.display = 'none';
					label.style.display = 'inline-block';
					deleteBtn.style.display = 'none';
				}
			});
					document.getElementById('cover-letter-delete-btn').addEventListener('click', function() {
					var fileInput = document.getElementById('cover-letter-file');
					var fileNameSpan = document.getElementById('cover-letter-filename');
					var label = document.getElementById('cover-letter-label');
					this.style.display = 'none';
					fileInput.value = '';
					fileNameSpan.textContent = '';
					fileNameSpan.style.display = 'none';
					label.style.display = 'inline-block';
				});

			

				document.getElementById('hear-about').addEventListener('change', function() {
					var otherInput = document.getElementById('other-source');
					if (this.value === 'other') {
						otherInput.style.display = 'block';
					} else {
						otherInput.style.display = 'none';
						otherInput.value = '';
					}
				});
				function getFormValue(e){
						e.preventDefault();

						const fullname = document.getElementById('fname')?.value;
						const email = document.getElementById('email')?.value;
					
						const countryCode = iti.getSelectedCountryData().dialCode; 
						const nationalNumber = iti.getNumber(intlTelInputUtils.numberFormat.NATIONAL).replace(/\D/g, '-'); 
						const phoneno = `+${countryCode} ${nationalNumber}`;

						const linkedin = document.getElementById('linkedin')?.value;
						const resume = document.getElementById('resume')?.files[0];
						const coverletterfile = document.getElementById('cover-letter-file')?.files[0];
						const coverlettertext = document.getElementById('cover-letter-text')?.value;
						const sourceoption = document.getElementById('hear-about')?.value;
						const sourcetext = document.getElementById('other-source')?.value;
						const isAgreed = document.getElementById('terms')?.checked;

						

						const formData = {
							name: fullname,
							email: email,
							phoneNumber: phoneno,
							linkedinUrl: linkedin,
							resume: resume,
							coverLetter: coverletterfile,
							coverLetterText: coverlettertext,
							referralSource: sourceoption,
							referralDescription: sourcetext,
							isAgreed: isAgreed
						};

						if (!resume || resume === undefined) {
							return alert("Please provide resume");
						}

						  if (!sourceoption) {
								return alert("Please select how you heard about us");
							}
							if (sourceoption === 'other' && !sourcetext.trim()) {
								return alert("Please specify the referral source");
							}
						postFormData(formData);
					}

					function postFormData(formData) {
						const payload= new FormData();
					payload.append("name",formData?.name ?? null);
					payload.append("email",formData?.email ?? null);
					payload.append("phoneNumber",formData?.phoneNumber ?? null);
					payload.append("linkedinUrl",formData?.linkedinUrl ?? null);
					payload.append("resume",formData?.resume ?? null);
					payload.append("coverLetter",formData?.coverLetter ?? null);
					payload.append("coverLetterText",formData?.coverLetterText ?? null);
					payload.append("referralSource",formData?.referralSource ?? null);
					payload.append("referralDescription",formData?.referralDescription ?? null);
					payload.append("isAgreed",formData?.isAgreed ?? null);

											
						fetch('https://devapin.precium.in/career', {
							method: 'POST',
							body: payload
						})
						.then(response => {
							if (!response.ok) throw new Error("error");
							return response.json();
						})
						.then(data => {
							document.getElementById('form-card').style.display='none';
							document.getElementById('thanku-card').style.display='block';
						})
						.catch(error => {
							alert('Error submitting form.');
							console.error('Error:', error);
						});
					}
					
					document.getElementById('ok-btn').addEventListener('click',function(){
						document.querySelector('form').reset();
						document.getElementById('resume-filename').style.display='none';
						document.getElementById('resume-label').style.display='inline-block';
						document.getElementById('cover-letter-filename').style.display='none';
						document.getElementById('cover-letter-label').style.display='inline-block';
						document.getElementById('other-source').style.display= 'none';
						document.getElementById('thanku-card').style.display='none';
						document.getElementById('form-card').style.display= 'block';
					})
				
					</script>
			
					<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
					<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"></script>

					<script>
					const input = document.querySelector("#phoneno");
					const iti = window.intlTelInput(input, {
						utilsScript:
						"https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
					});
					</script>

	</div>
	<my-footer></my-footer>
	
	<script src=/js/stickUp.min.js></script>
	<script type="text/javascript">
		jQuery(function (a) { a(document).ready(function () { a(".navbar-inverse").stickUp() }) });
	</script>
</body>
</html>
