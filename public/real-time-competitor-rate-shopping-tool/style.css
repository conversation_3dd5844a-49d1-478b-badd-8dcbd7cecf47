.cus-header {
  display: grid;
  gap: 20px;
  padding: 2vw 9vw 1vw 9vw;
  grid-template-columns: 7fr 3fr;
}
.cus-heading h1 {
  color: #306fbc;
  margin: 0;
  padding-top: 30px;
}

.cus-desc {
  font-style: italic;
  margin: 0;
  margin-top: -35px;
}

.cus-image img {
  margin-top: -22px;
  width: 100%;
  max-width: 282px;
}
@media (min-width: 1200px) {
  .cus-heading {
    grid-column: 1;
    grid-row: 1;
  }

  .cus-image {
    grid-column: 2;
    grid-row: 1 / span 2;
  }

  .div-desc {
    grid-column: 1;
    grid-row: 2;
  }
}
@media (max-width: 1200px) {
  .cus-header {
    display: grid;
    grid-template-columns: 60% 40%;
    grid-template-rows: 73% 38%;
    row-gap: 16px;
    padding: 2vw 9vw 0vw 9vw;
  }
  .div-desc {
    grid-column: 1 / span 2;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
}

@media (max-width: 600px) {
  .cus-header {
    display: grid;
    grid-template-columns: 100% !important;
    padding: 2vw 9vw 2vw 9vw;
    grid-template-rows: 50% 38% 40% !important;
    row-gap: 5px !important;
  }
  .cus-heading h1{
    padding-top: 0;
  }
  .cus-image {
    grid-row: 1;
  }
  .div-desc {
    grid-column: auto !important;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
  .cus-image img {
    height: 250px;
  }
}
